"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Search,
  Plus,
  Play,
  Pause,
  Settings,
  Trash2,
  Activity,
  Clock,
  CheckCircle,
  Workflow,
} from "lucide-react";
import { N8nWorkflow } from "@/types/n8n";

export default function MyWorkflowsPage() {
  const [workflows, setWorkflows] = useState<N8nWorkflow[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadWorkflows();
  }, []);

  const loadWorkflows = async () => {
    try {
      const response = await fetch("/api/n8n/workflows");
      const result = await response.json();

      if (result.success) {
        setWorkflows(result.data || []);
      } else {
        console.error("Failed to load workflows:", result.error);
        // Mock data for demo
        setWorkflows([
          {
            id: "1",
            name: "Telegram Support Bot",
            active: true,
            nodes: [],
            connections: {},
            createdAt: "2024-01-15T10:00:00Z",
            updatedAt: "2024-01-15T10:00:00Z",
            tags: ["telegram", "support"],
          },
          {
            id: "2",
            name: "WhatsApp Order Notifications",
            active: false,
            nodes: [],
            connections: {},
            createdAt: "2024-01-14T15:30:00Z",
            updatedAt: "2024-01-14T15:30:00Z",
            tags: ["whatsapp", "orders"],
          },
          {
            id: "3",
            name: "Email Newsletter Automation",
            active: true,
            nodes: [],
            connections: {},
            createdAt: "2024-01-13T09:15:00Z",
            updatedAt: "2024-01-13T09:15:00Z",
            tags: ["email", "marketing"],
          },
        ]);
      }
    } catch (error) {
      console.error("Error loading workflows:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleWorkflow = async (workflowId: string, currentStatus: boolean) => {
    try {
      const action = currentStatus ? "deactivate" : "activate";
      const response = await fetch(
        `/api/n8n/workflows/${workflowId}/${action}`,
        {
          method: currentStatus ? "DELETE" : "POST",
        }
      );

      const result = await response.json();

      if (result.success) {
        setWorkflows((prev) =>
          prev.map((workflow) =>
            workflow.id === workflowId
              ? { ...workflow, active: !currentStatus }
              : workflow
          )
        );
      } else {
        console.error("Failed to toggle workflow:", result.error);
      }
    } catch (error) {
      console.error("Error toggling workflow:", error);
    }
  };

  const deleteWorkflow = async (workflowId: string) => {
    if (!confirm("Are you sure you want to delete this workflow?")) {
      return;
    }

    try {
      const response = await fetch(`/api/n8n/workflows?id=${workflowId}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (result.success) {
        setWorkflows((prev) =>
          prev.filter((workflow) => workflow.id !== workflowId)
        );
      } else {
        console.error("Failed to delete workflow:", result.error);
      }
    } catch (error) {
      console.error("Error deleting workflow:", error);
    }
  };

  const filteredWorkflows = workflows.filter(
    (workflow) =>
      workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      workflow.tags?.some((tag) =>
        tag.toLowerCase().includes(searchTerm.toLowerCase())
      )
  );

  const getStatusIcon = (active: boolean) => {
    return active ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <Clock className="h-4 w-4 text-gray-500" />
    );
  };

  const getStatusBadge = (active: boolean) => {
    return active ? (
      <Badge variant="success">Active</Badge>
    ) : (
      <Badge variant="secondary">Inactive</Badge>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                My Workflows
              </h1>
              <p className="text-gray-600 mt-1">
                View and manage your personal n8n automation workflows
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/connections">
                <Button variant="outline">Manage Connections</Button>
              </Link>
              <Link href="/workflows">
                <Button variant="outline">Browse Templates</Button>
              </Link>
              <Button disabled>
                <Plus className="mr-2 h-4 w-4" />
                View Only Mode
              </Button>
            </div>
          </div>
        </div>

        {/* Search and Stats */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search workflows..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <span>{workflows.length} total</span>
              <span>{workflows.filter((w) => w.active).length} active</span>
            </div>
          </div>
        </div>

        {/* Workflows Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredWorkflows.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredWorkflows.map((workflow) => (
              <Card
                key={workflow.id}
                className="hover:shadow-md transition-shadow"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg flex items-center space-x-2">
                        {getStatusIcon(workflow.active)}
                        <span>{workflow.name}</span>
                      </CardTitle>
                      <CardDescription className="mt-1">
                        Created{" "}
                        {new Date(workflow.createdAt).toLocaleDateString()}
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() =>
                          toggleWorkflow(workflow.id, workflow.active)
                        }
                        title={workflow.active ? "Deactivate" : "Activate"}
                      >
                        {workflow.active ? (
                          <Pause className="h-4 w-4" />
                        ) : (
                          <Play className="h-4 w-4" />
                        )}
                      </Button>
                      <Button variant="ghost" size="icon" title="Settings">
                        <Settings className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => deleteWorkflow(workflow.id)}
                        title="Delete"
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-3">
                    {/* Status */}
                    <div className="flex items-center justify-between">
                      {getStatusBadge(workflow.active)}
                      <div className="flex items-center text-sm text-gray-500">
                        <Activity className="h-3 w-3 mr-1" />
                        {workflow.nodes?.length || 0} nodes
                      </div>
                    </div>

                    {/* Tags */}
                    {workflow.tags && workflow.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {workflow.tags.slice(0, 3).map((tag) => (
                          <Badge
                            key={tag}
                            variant="outline"
                            className="text-xs"
                          >
                            {tag}
                          </Badge>
                        ))}
                        {workflow.tags.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{workflow.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex items-center space-x-2 pt-2">
                      <Button variant="outline" size="sm" className="flex-1" disabled>
                        View Only
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Activity className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Workflow className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No workflows found
            </h3>
            <p className="text-gray-500 mb-6">
              {searchTerm
                ? "No workflows match your search."
                : "No workflows found. Create workflows in n8n and they will appear here."}
            </p>
            {!searchTerm && (
              <div className="flex justify-center space-x-4">
                <Link href="/connections">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Connect Services
                  </Button>
                </Link>
                <Link href="/workflows">
                  <Button variant="outline">
                    Browse Templates
                  </Button>
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
